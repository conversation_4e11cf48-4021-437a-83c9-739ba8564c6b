#!/usr/bin/env python3
"""
Odoo Development Runner
Interactive script to manage Odoo development server with commands:
- r: restart Odoo
- e: exit runner
- k: kill Odoo process
- s: start Odoo
- d: start/stop Docker PostgreSQL
- l: show logs
- h: help
"""

import os
import sys
import subprocess
import signal
import time
import threading
from pathlib import Path

class OdooRunner:
    def __init__(self):
        self.odoo_process = None
        self.docker_running = False
        self.base_dir = Path(__file__).parent
        self.venv_python = self.base_dir / "venv" / "bin" / "python"
        self.odoo_bin = self.base_dir / "odoo-bin"
        self.config_file = self.base_dir / "odoo.conf"
        
    def check_docker_status(self):
        """Check if Docker PostgreSQL is running"""
        try:
            result = subprocess.run(
                ["docker", "compose", "ps", "-q", "postgres"],
                capture_output=True, text=True, cwd=self.base_dir
            )
            self.docker_running = bool(result.stdout.strip())
            return self.docker_running
        except Exception:
            return False
    
    def start_docker(self):
        """Start Docker PostgreSQL"""
        print("🐳 Starting PostgreSQL container...")
        try:
            subprocess.run(
                ["docker", "compose", "up", "-d", "postgres"],
                cwd=self.base_dir, check=True
            )
            print("✅ PostgreSQL container started")
            self.docker_running = True
            
            # Wait for PostgreSQL to be ready
            print("⏳ Waiting for PostgreSQL to be ready...")
            for i in range(30):
                result = subprocess.run(
                    ["docker", "compose", "exec", "postgres", "pg_isready", "-U", "odoo"],
                    capture_output=True, cwd=self.base_dir
                )
                if result.returncode == 0:
                    print("✅ PostgreSQL is ready")
                    return True
                time.sleep(1)
            print("❌ PostgreSQL failed to start within 30 seconds")
            return False
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to start PostgreSQL: {e}")
            return False
    
    def stop_docker(self):
        """Stop Docker PostgreSQL"""
        print("🐳 Stopping PostgreSQL container...")
        try:
            subprocess.run(
                ["docker", "compose", "down"],
                cwd=self.base_dir, check=True
            )
            print("✅ PostgreSQL container stopped")
            self.docker_running = False
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to stop PostgreSQL: {e}")
    
    def start_odoo(self):
        """Start Odoo server"""
        if self.odoo_process and self.odoo_process.poll() is None:
            print("⚠️  Odoo is already running")
            return
        
        if not self.check_docker_status():
            print("🐳 PostgreSQL not running, starting it...")
            if not self.start_docker():
                return
        
        print("🚀 Starting Odoo server...")
        try:
            cmd = [
                str(self.venv_python),
                str(self.odoo_bin),
                "-c", str(self.config_file),
                "-d", "odoo_clean",
                "--dev=reload,qweb,werkzeug,xml"
            ]
            
            self.odoo_process = subprocess.Popen(
                cmd,
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Start log monitoring in a separate thread
            log_thread = threading.Thread(target=self._monitor_logs, daemon=True)
            log_thread.start()
            
            print("✅ Odoo server started")
            print("🌐 Access Odoo at: http://localhost:8069")
            
        except Exception as e:
            print(f"❌ Failed to start Odoo: {e}")
    
    def _monitor_logs(self):
        """Monitor Odoo logs in background"""
        if not self.odoo_process:
            return
        
        for line in iter(self.odoo_process.stdout.readline, ''):
            if line:
                print(f"[ODOO] {line.rstrip()}")
            if self.odoo_process.poll() is not None:
                break
    
    def kill_odoo(self):
        """Kill Odoo process"""
        if self.odoo_process and self.odoo_process.poll() is None:
            print("🔪 Killing Odoo process...")
            self.odoo_process.terminate()
            try:
                self.odoo_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.odoo_process.kill()
                self.odoo_process.wait()
            print("✅ Odoo process killed")
        else:
            print("⚠️  No Odoo process running")
    
    def restart_odoo(self):
        """Restart Odoo server"""
        print("🔄 Restarting Odoo...")
        self.kill_odoo()
        time.sleep(2)
        self.start_odoo()
    
    def show_status(self):
        """Show current status"""
        docker_status = "🟢 Running" if self.check_docker_status() else "🔴 Stopped"
        odoo_status = "🟢 Running" if (self.odoo_process and self.odoo_process.poll() is None) else "🔴 Stopped"
        
        print(f"""
📊 Status:
   PostgreSQL: {docker_status}
   Odoo:       {odoo_status}
   Config:     {self.config_file}
   URL:        http://localhost:8069
        """)
    
    def show_help(self):
        """Show help message"""
        print("""
🚀 Odoo Development Runner Commands:
   r  - Restart Odoo server
   s  - Start Odoo server
   k  - Kill Odoo process
   d  - Toggle Docker PostgreSQL (start/stop)
   l  - Show current status
   h  - Show this help
   e  - Exit runner
   
🌐 Access Odoo at: http://localhost:8069
📧 Default admin: admin / admin
        """)
    
    def run(self):
        """Main interactive loop"""
        print("🚀 Odoo Development Runner")
        print("Type 'h' for help, 'e' to exit")
        
        # Show initial status
        self.show_status()
        
        try:
            while True:
                try:
                    command = input("\n> ").strip().lower()
                    
                    if command == 'e':
                        print("👋 Exiting...")
                        self.kill_odoo()
                        break
                    elif command == 'r':
                        self.restart_odoo()
                    elif command == 's':
                        self.start_odoo()
                    elif command == 'k':
                        self.kill_odoo()
                    elif command == 'd':
                        if self.check_docker_status():
                            self.stop_docker()
                        else:
                            self.start_docker()
                    elif command == 'l':
                        self.show_status()
                    elif command == 'h':
                        self.show_help()
                    elif command == '':
                        continue
                    else:
                        print(f"❓ Unknown command: {command}. Type 'h' for help.")
                        
                except KeyboardInterrupt:
                    print("\n👋 Exiting...")
                    self.kill_odoo()
                    break
                except EOFError:
                    print("\n👋 Exiting...")
                    self.kill_odoo()
                    break
                    
        finally:
            # Cleanup
            if self.odoo_process and self.odoo_process.poll() is None:
                self.kill_odoo()

if __name__ == "__main__":
    runner = OdooRunner()
    runner.run()
